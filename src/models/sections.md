sections schema:
{
salesPrice: {type: Number, default: 0}, // Sales price for the section
availableCredits: {
type: Number,
default: function (this: any) {
return this.salesPrice || 0
},
}, // available credits for the section
}

topUp price: salesPrice - availableCredits

I have an associated task(AT) product for students and teachers.
A student buys an AT, then can book a teacher to teach the student(teacher gets paid some amount).
Each AT has certain number of sections each having different values.
If an AT is bought using 1000 USD, a AT has 5 sections, then each sections can have let's say:
200, 300, 100, 200, 200 USD credits each.
Now each section will be completed from first till last.
A student books a teacher for the first section, if they cancels after 12 hours from booked time, then
out of 200 credit, 20% will be deducted. So now the student has to buy deficit amount of 40 USD to topup the section.
Now we have 2 order ids, the initial order id, then the new order id of the top up.
We also have the option to cancel an order from order details page, in that case only the credit amount from that particular order will be deducted and refunded to the user. So how to handle this? We have separate order collection, and the price of the order mayt differ from the price of the section. The section price is the actual product price, but the order price may be lower because of discount based on discount period when the product is bought.

My only question is how to associate the credits of a particular section with the order id? So that refund will be easy during cancellation. Also cancellation can be possible only when the section is not booked with any teacher.

This is a complex credit management system. Here's how I'd recommend structuring the data to handle the association between section credits and order IDs:

## Recommended Schema Structure

### 1. Enhanced Sections Schema

```javascript
{
  salesPrice: { type: Number, default: 0 },
  availableCredits: {
    type: Number,
    default: function() { return this.salesPrice || 0 }
  },
  creditSources: [{
    orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'Order', required: true },
    creditAmount: { type: Number, required: true },
    isActive: { type: Boolean, default: true }, // false when refunded
    createdAt: { type: Date, default: Date.now }
  }],
  // Other section fields...
}
```

### 2. Order Schema

```javascript
{
  _id: ObjectId,
  userId: ObjectId,
  orderType: { type: String, enum: ['INITIAL_PURCHASE', 'TOPUP'], required: true },
  totalAmount: Number,
  discountApplied: Number,
  actualAmount: Number, // amount after discount
  sectionAllocations: [{
    sectionId: ObjectId,
    allocatedCredits: Number
  }],
  status: { type: String, enum: ['COMPLETED', 'REFUNDED'], default: 'COMPLETED' },
  createdAt: Date
}
```

## Implementation Strategy

### 1. Initial Purchase Flow

```javascript
// When AT is purchased
async function processPurchase(userId, atId, orderAmount, sectionAllocations) {
  const order = new Order({
    userId,
    orderType: 'INITIAL_PURCHASE',
    totalAmount: calculateOriginalPrice(sectionAllocations),
    actualAmount: orderAmount,
    discountApplied: calculateOriginalPrice(sectionAllocations) - orderAmount,
    sectionAllocations,
  })

  await order.save()

  // Update each section with credit source
  for (const allocation of sectionAllocations) {
    await Section.findByIdAndUpdate(allocation.sectionId, {
      $push: {
        creditSources: {
          orderId: order._id,
          creditAmount: allocation.allocatedCredits,
        },
      },
      $inc: {availableCredits: allocation.allocatedCredits},
    })
  }
}
```

### 2. Top-up Flow

```javascript
async function processTopUp(userId, sectionId, topUpAmount) {
  const topUpOrder = new Order({
    userId,
    orderType: 'TOPUP',
    totalAmount: topUpAmount,
    actualAmount: topUpAmount,
    discountApplied: 0,
    sectionAllocations: [
      {
        sectionId,
        allocatedCredits: topUpAmount,
      },
    ],
  })

  await topUpOrder.save()

  // Add credit source to section
  await Section.findByIdAndUpdate(sectionId, {
    $push: {
      creditSources: {
        orderId: topUpOrder._id,
        creditAmount: topUpAmount,
      },
    },
    $inc: {availableCredits: topUpAmount},
  })
}
```

### 3. Order Cancellation/Refund Flow

```javascript
async function cancelOrder(orderId) {
  const order = await Order.findById(orderId)

  if (order.status === 'REFUNDED') {
    throw new Error('Order already refunded')
  }

  // Check if any sections are currently booked
  for (const allocation of order.sectionAllocations) {
    const section = await Section.findById(allocation.sectionId)
    const hasActiveBookings = await checkActiveBookings(allocation.sectionId)

    if (hasActiveBookings) {
      throw new Error('Cannot refund - section has active bookings')
    }
  }

  // Process refund for each section
  for (const allocation of order.sectionAllocations) {
    await Section.findOneAndUpdate(
      {
        _id: allocation.sectionId,
        'creditSources.orderId': orderId,
        'creditSources.isActive': true,
      },
      {
        $set: {'creditSources.$.isActive': false},
        $inc: {availableCredits: -allocation.allocatedCredits},
      }
    )
  }

  // Mark order as refunded
  order.status = 'REFUNDED'
  await order.save()

  // Process actual refund to payment gateway
  await processPaymentRefund(orderId, order.actualAmount)
}
```

### 4. Credit Deduction (for cancellation penalties)

```javascript
async function deductCreditsPenalty(sectionId, penaltyAmount) {
  const section = await Section.findById(sectionId)

  // Deduct from most recent credits first (LIFO)
  let remainingPenalty = penaltyAmount

  for (let i = section.creditSources.length - 1; i >= 0 && remainingPenalty > 0; i--) {
    const creditSource = section.creditSources[i]

    if (!creditSource.isActive) continue

    const deductionAmount = Math.min(creditSource.creditAmount, remainingPenalty)
    creditSource.creditAmount -= deductionAmount
    remainingPenalty -= deductionAmount

    if (creditSource.creditAmount === 0) {
      creditSource.isActive = false
    }
  }

  section.availableCredits -= penaltyAmount
  await section.save()
}
```

## Key Benefits of This Approach

1. **Clear Audit Trail**: Every credit has a traceable source (order ID)
2. **Selective Refunds**: Can refund specific orders without affecting others
3. **Penalty Handling**: Can deduct penalties while maintaining order association
4. **Flexible Top-ups**: Multiple top-ups create separate, trackable credit sources
5. **Business Rules**: Easy to implement rules like "no refund if section is booked"

## Additional Considerations

1. **Indexes**: Add compound indexes on `(sectionId, creditSources.orderId)` for efficient queries
2. **Validation**: Add schema validation to ensure `availableCredits` always matches sum of active credit sources
3. **Transactions**: Use MongoDB transactions for multi-document operations to maintain consistency
4. **Logging**: Log all credit operations for audit purposes

This structure gives you full traceability while keeping the refund logic straightforward and maintainable.

---

I am using mongodb + feathersjs

order{
\_id,
links: [{
id,
style,
count
}]
}

service_pack{
\_id,
sections: [{
name,
price,
}]
}

service_pack_user{
\_id,
uid,
orderId,
servicePackId: service_pack.\_id
}

section{
\_id,
servicePackUserId,
availableCredits,
creditSources: [{
orderId,
creditAmount,
isActive,
}]
}

Above are my db schema. So we buy a service package. Each package has certain number of sections. Each section has a price. When we buy a package, we can either buy the first section or all the sections. All other sections will be shown as locked. If user finds the first section useful, they can buy all the rest remaining section(they have to buy all the remaining sections, cannot buy them individually).
If we cancel a section, we get a penalty. The penalty is deducted from the available credits. If the available credits are not enough, we need to top up. When we top up, we add more credits to the section.

Based on above scenario, we have 3 kinds of orders:

1. Buy first or all section

- here in order.links we store {
  \_id: service_pack.\_id,
  style: 'service',
  count: 1,
  }

- we create docs for all the sections but only activate the first section(if count: 1)
- one service_pack_user is created for the user and corresponding orderId and servicePackId

2. Top up a particular section

- here in order.links we store {
  \_id: section.\_id,
  style: 'section_top_up',
  }
- here we simply top up the section.availableCredits and add a new entry in section.creditSources

3. Buy remaining sections

- here I need your help how to handle the order
- primarily what id should I put in links
