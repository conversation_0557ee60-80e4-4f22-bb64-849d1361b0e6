## This Week

- Merge frontend gift cards & service-task - Wed ✅
- Order cancel optimize logic - Wed ✅
- AT Ticket unclaim - Wed ✅
- AT Ticket cancel - Wed ✅ [Already handled]
- AT Order cancel - Wed ✅
  - refund calc ✅
  - 30 days refund logic ✅
  - credit deduction ✅
  - 14 days logic - after that only cancel associated task ✅ [Already handled]
- Batch Logic in Order refund
- Batch Logic in Ticket refund
  ***
- Separate Lecture Cancel - Thu ✅ [Already handled]
- First session free on order cancellation - Fri
- Testing & Bugfixes - Fri - Sat

### Misc

- Associate task orders shoould not be purchased using points
- AT Ticket claim timeout issue

* noDiscount case. Maybe only for service_premium? Action: Removed
* is item.isOnCampus for 1v1 mentoring also? Ans: only for substitute

### Demo Session Refund

- First order M1, (pack.\_id of M1)
- First order of Service Package S1, take L1 (pack.\_id of S1)

### Tips

- In order details page `Cancel Button` new logic

  - If `cancelDisabled` === true, disable the button, on hover show `message`.
  - If `refundAllowed` === false, hide the button.
  - If order.isSchool && type === service_premium && links.some((item) => item.style === 'service')
    - show popover menu on clicking/hovering the button
      - Lecture - on click open `new lecture dialog` and on `ok` call `new function` to cancel lecture.
      - Voucher - on click call `cancelTicket()`
  - Else just the button
  - Then on Cancel Button Click
  - `cancelDisabled` === true && links.some((item) => item.style === 'service' && item.type === 'serviceTask')
    - Open the dialog: "Are you sure..........to cancel only the associated task?"
    - On `Ok`, call `new function` to cancel associated task
  - else normal flow with: OrderDetailDialog
  - Remove `cancelTicket()`'s `if block`.

- How to get `Lectures` for `Lecture Dialog`

  - links.filter((item) => item.style === 'service_premium')

- How to get ids of associated task to cancel?

  - links.filter((item) => item.style === 'service' && item.type === 'serviceTask')

- For `new function` to cancel lecture(s) or associated tasks(s)
  - simply call App.service('order').get('cancel', {query: {id: id.value, status: 500, `linkId`: [Array of link ids you wish to cancel]}})
