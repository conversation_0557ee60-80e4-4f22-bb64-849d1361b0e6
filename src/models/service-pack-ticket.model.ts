// service-pack-ticket-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'servicePackTicket'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      // code: {type: String}, // 编号
      uid: {type: String},
      school: {type: String, required: true}, // school-plan._id
      servicePremium: {type: String, required: true}, // service-pack._id
      order: {type: String, trim: true}, // 关联 order
      serviceData: {
        type: [
          {
            servicePack: {type: String}, // service-pack._id
            mentorPack: {type: String}, // // For Associate Task Parent Mentoring service-pack._id
            cash: {type: Number, default: 0}, // 现金购买数量 当前剩余
            point: {type: Number, default: 0}, // 积分购买数量 当前剩余
            gift: {type: Number, default: 0}, // 赠送数量 当前剩余
            cashOrigin: {type: Number, default: 0}, // 现金购买数量
            pointOrigin: {type: Number, default: 0}, // 积分购买数量
            giftOrigin: {type: Number, default: 0}, // 赠送数量
          },
        ],
      },
      refund: {type: Boolean, default: false}, // 已退款
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
