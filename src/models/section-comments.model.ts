import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'section-comments'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient

  const commentSchema = new Schema(
    {
      uid: {type: String, required: true}, // user ID who made the comment
      comment: {type: String, trim: true},
      createdAt: {type: Date, default: Date.now},
    },
    {_id: false}
  )

  const fileSchema = new Schema({
    uid: {type: String, required: true}, // user ID who uploaded the file
    name: [{type: String, trim: true}],
    nickname: {type: String, trim: true},
    avatar: {type: String},
    cover: {type: String, trim: true},
    mime: {type: String, trim: true},
    uploadedTime: {type: Date, default: Date.now},
  })

  const sectionCommentsSchema = new Schema(
    {
      uid: {type: String, required: true}, // user ID who made the comment
      sectionId: {type: String, required: true},
      files: fileSchema,
      comments: [commentSchema],
      del: {type: Boolean, default: false},
    },
    {timestamps: true}
  )

  sectionCommentsSchema.index({sectionId: 1})

  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }

  return mongooseClient.model<any>(modelName, sectionCommentsSchema)
}
