// service-pack-user-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'servicePackUser'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      uid: {type: String, required: true}, // users._id
      total: {type: Number, default: 0}, // 总次数 会变化，代课服务下为分钟数 Total times will vary, in the case of substitute teaching, it is minutes
      used: {type: Number, default: 0}, // 已经使用，代课服务下为分钟数 Already used, substitute teaching service is minutes
      expireSoon: {type: Date}, // 即将过期的时间 Time to expire
      session: {
        _id: {type: String}, // 关联session._id Associated with session._id
        name: {type: String}, // session.name
        start: {type: String}, // session.start
        end: {type: String}, // session.end
      },
      snapshot: {type: Schema.Types.Mixed, required: true}, // service-pack 快照，主题服务包下的Lecture包用 service-auth 快照     service-pack snapshot, Lecture package under the theme service pack uses service-auth snapshot
      associatedTaskStatus: {type: String, enum: ['unassigned', 'cancelled', 'ongoing', 'completed', 'refunded']},
      associatedBooking: {type: String},
      associatedServicer: {
        uid: {type: Schema.Types.ObjectId, ref: 'users'}, // Reference to the user
        name: {
          type: [{type: String, trim: true}],
          default: undefined,
        }, // Name of the user
        nickname: {type: String, trim: true}, // Nickname of the user
        avatar: {type: String}, // Avatar of the user
        assignedAt: {type: Date},
      },
      sectionCreditDeficit: {
        sectionId: {type: String, index: true}, // section._id
        points: {type: Number}, // deficient amount
      },
      status: {type: Boolean, default: true},
      order: {type: String, required: true}, // 关联 order
      price: {type: Number, default: 0}, // 订单金额 Order amount
      point: {type: Number}, // 订单支付积分
      isPoint: {type: Boolean, default: false}, // 现金购买/积分购买
      payMethod: {type: String}, // 默认为空, 现金支付过就会更新为 cash， https://github.com/zran-nz/bug/issues/5020
      // 主题服务包用 https://github.com/zran-nz/bug/issues/5196
      pid: {type: String}, // 主题服务包的主包, 本身用于Lecture包，一个主题服务包包含多个Lecture包   The main package of the theme service package is used for the Lecture package. A theme service package contains multiple Lecture packages.
      premium: {type: String}, // Lecture包 对应的 service-auth 认证的精品课 service-auth._id
      taskIndex: {type: [String]}, // Lecture包下, 课件去重后的索引 https://github.com/zran-nz/bug/issues/5200
      tasks: {type: [String]}, // Lecture包下, 需要预约的课件id，用于自动计算出下次预约的关联的课件，增加：首次购买/补买/取消预约，扣除：预约，[id1, id2, ...] https://github.com/zran-nz/bug/issues/5200 //Under the Lecture package, the courseware id that needs to be booked is used to automatically calculate the associated courseware for the next booking, add: first purchase/re-purchase/cancel booking, deduct: booking, [id1, id2, ...]
      // 线下包
      country: {type: String}, // 国家
      city: {type: String}, // 城市
      address: {type: String, trim: true}, //地址
      place_id: {type: String, trim: true}, // google地点id
      location: {
        type: {
          type: String,
          enum: ['Point'],
        },
        coordinates: {
          type: [Number],
        },
      },
      servicePremium: {type: String}, // service-pack._id lecture下有此主题服务包id service-pack._id lecture contains the service pack id of this topic
      participants: {type: [String]}, // uid
      zoom: {
        enabled: {type: Boolean},
        maxParticipants: {type: Boolean},
        max: {type: Number},
        min: {type: Number},
      },
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
