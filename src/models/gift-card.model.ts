// gift-card-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'giftCard'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      code: {type: String, required: true, unique: true, trim: true}, // Unique redemption code
      amount: {type: Number, required: true}, // Original amount in cents

      // Purchase information
      creator: {type: String, required: true}, // user._id or school-plan._id of who purchased it
      creatorType: {type: String, enum: ['user', 'school'], required: true}, // Indicates if purchased by user or school
      creatorAdmin: {type: String}, // If creatorType is 'school', the user._id of the school admin who made the purchase

      // Redemption information
      redeemer: {type: String}, // user._id or school-plan._id of who redeemed it (null if not redeemed)
      redeemerType: {type: String, enum: ['user', 'school']}, // Indicates if redeemed by user or school
      redeemerAdmin: {type: String}, // If redeemerType is 'school', the user._id of the school admin who redeemed it

      status: {
        type: String,
        required: true,
        enum: ['active', 'redeemed', 'canceled'],
        default: 'active',
      },
      redeemedAt: {type: Date}, // When the gift card was redeemed

      isGift: {type: Boolean, default: false}, // Whether it was sent as a gift
      recipientEmail: {type: String, trim: true}, // Email address if sent as gift
      senderName: {type: String, trim: true}, // Name of the person sending the gift
      recipientName: {type: String, trim: true}, // Name of the gift recipient
      giftMessage: {type: String, trim: true}, // Personal message for the gift

      image: {type: String, trim: true}, // Gift card design image URL

      order: {type: String, required: true}, // order._id that created this gift card
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
