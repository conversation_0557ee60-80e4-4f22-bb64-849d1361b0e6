import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'section'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      name: {type: String, trim: true, required: true}, // Name of the section
      prompt: {type: String, trim: true}, // Prompt for the section
      salesPrice: {type: Number, default: 0}, // Sales price for the section
      costPrice: {type: Number, default: 0}, // Cost price for the section
      sectionNo: {type: Number},
      uid: {type: Schema.Types.ObjectId, ref: 'users'}, // Reference to the user
      status: {type: String, enum: ['pending', 'ongoing', 'completed', 'refunded'], default: 'pending'}, // Status of the section
      serviceTaskId: {type: Schema.Types.ObjectId, ref: 'servicePack', required: true, index: true}, // Reference to service-pack-user._id
      markAsCompleted: {type: Boolean, default: false}, // Flag to mark the section as completed by the service provider
      markAsCompletedTime: {type: Date}, // Time when the section was marked as completed by the service provider
      lastAssignedTime: {type: Date}, // Last assigned time for the section
      lastCancelledTime: {type: Date},
      completedTime: {type: Date}, // Time when the section was completed
      isViewAppeal: {type: Boolean}, //when approved it will become true, after teacher checks  for the first time, it will become false
      completedBy: {type: Schema.Types.ObjectId, ref: 'users'}, // Reference to the user(service provider) who supervised the section, updated when cancelled or completed the whole task(all the sections)
      servicer: {type: String},
      bookingId: {type: String},
      taskDetails: {type: Object},
      cancelledServicer: [
        {
          userId: {type: Schema.Types.ObjectId, ref: 'users'},
          cancelledAt: {type: Date},
          cancelledByYou: {type: Boolean, default: false},
          compensation: {type: Number},
        },
      ],
      availableCredits: {
        type: Number,
        default: function (this: any) {
          return this.salesPrice || 0
        },
      }, // available credits for the section
      creditSources: [
        {
          orderId: {type: Schema.Types.ObjectId, ref: 'Order', required: true, index: true},
          total: {type: Number, required: true},
          available: {type: Number, required: true},
          isActive: {type: Boolean, default: true}, // false when refunded
          createdAt: {type: Date, default: Date.now},
        },
      ],
    },
    {
      timestamps: true,
    }
  )

  schema.index({'creditSources.orderId': 1})

  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
