// Initializes the `gift-card` service on path `/gift-card`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {GiftCard} from './gift-card.class'
import createModel from '../../models/gift-card.model'
import hooks from './gift-card.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'gift-card': GiftCard & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
  }

  app.use('/gift-card', new GiftCard(options, app))

  const service = app.service('gift-card')

  service.hooks(hooks)
}
