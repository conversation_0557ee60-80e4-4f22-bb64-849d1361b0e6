import * as authentication from '@feathersjs/authentication'
import {HookContext} from '@feathersjs/feathers'
import hook from '../../hook'

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [hook.disallowExternal],
    update: [hook.disallow],
    patch: [hook.disallow], // Transactions should not be modified after creation
    remove: [hook.disallow], // Transactions should not be removed
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
