// Initializes the `gift-card-log` service on path `/gift-card-log`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {GiftCardLog} from './gift-card-log.class'
import createModel from '../../models/gift-card-log.model'
import hooks from './gift-card-log.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'gift-card-log': GiftCardLog & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/gift-card-log', new GiftCardLog(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('gift-card-log')

  service.hooks(hooks)
}
