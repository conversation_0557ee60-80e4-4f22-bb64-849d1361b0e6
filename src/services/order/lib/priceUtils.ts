interface Order {
  links: Array<{id: string; price: number}>
  price: number
  cash?: number
  giftCard?: number
}
export function allocatePayments(order: Order) {
  const totalPrice = order.price
  const giftCardAmount = order.giftCard || 0

  let totalGiftCardAllocated = 0

  const updatedLinks = order.links.map((link, index) => {
    const proportion = link.price / totalPrice
    let giftCardForItem = Math.round(giftCardAmount * proportion)

    if (index === order.links.length - 1) {
      giftCardForItem = giftCardAmount - totalGiftCardAllocated
    } else {
      totalGiftCardAllocated += giftCardForItem
    }

    const cashForItem = link.price - giftCardForItem

    return {
      ...link,
      giftCard: giftCardForItem,
      cash: cashForItem,
    }
  })

  return {
    ...order,
    links: updatedLinks,
  }
}

export function calculateRefundBreakUp(
  item: {
    price: number
    isPoint?: boolean
    giftCard?: number
    cash?: number
    refund: Array<{amount: number; method: string}>
    refundCash?: number // remove
  },
  refundAmount: number
) {
  if (!refundAmount || refundAmount <= 0 || item.isPoint)
    return {
      cashRefund: 0,
      giftCardRefund: 0,
    }
  let giftCardRefunded = 0
  if (Array.isArray(item.refund) && item.refund.length > 0) {
    console.log('Refund:', item.refund)
    giftCardRefunded = item.refund.reduce((sum, refund) => {
      return sum + (refund.method === 'giftCard' ? refund.amount : 0)
    }, 0)
  }

  const actualRefundAmount = Math.min(refundAmount, item.price)

  const remainingGiftCard = (item.giftCard || 0) - giftCardRefunded
  console.log('GiftCard Refunded:', giftCardRefunded)
  console.log('Remaining Gift Card:', remainingGiftCard)
  const giftCardRefund = Math.min(actualRefundAmount, remainingGiftCard)
  const cashRefund = actualRefundAmount - giftCardRefund

  return {
    cashRefund,
    giftCardRefund,
  }
}
