import {Application} from '../../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession, TxnParams} from '../../../dbTransactions'
import {calculateRefundBreakUp} from './priceUtils'

declare const SiteUrl: string
declare const hashToUrl: (hash: string) => string

export class OrderRecovery {
  adminEmails: string[]
  maxRetryCount: number
  constructor(private app: Application) {
    this.adminEmails = ['<EMAIL>']
    this.maxRetryCount = 3
  }

  /**
   * Main entry point for order completion, after successful payment.
   * @param orderId The ID of the order to process.
   * @param paymentDetails Payment information, only used on the first API call.
   * @param source The source of the call: 'api' or 'webhook'.
   */
  async processOrderCompletion(orderId: string, paymentDetails: any, source: 'api' | 'webhook') {
    const lockDurationMinutes = 3
    const lockExpiresAt = new Date(Date.now() + lockDurationMinutes * 60 * 1000)

    const originalOrder: any = await this.app
      .service('order')
      .Model.findOneAndUpdate(
        {
          _id: orderId,
          status: {$in: [100, 110]},
          $or: [{processingLockExpiresAt: {$exists: false}}, {processingLockExpiresAt: {$lt: new Date()}}],
        },
        {
          $set: {
            processingLockExpiresAt: lockExpiresAt,
            status: 110,
          },
        }
        // We do NOT use { new: true }, so we get the document *before* this update.
      )
      .lean()
    if (!originalOrder) {
      if (source === 'webhook') {
        const orderInfo = (await this.app.service('order').Model.findById(orderId).select('status')) as any
        if ([100, 110].includes(orderInfo?.status)) throw new Error(`Order ${orderId} is locked. Webhook will retry.`)
      }
      return
    }

    let orderToProcess = originalOrder

    // This is only done if this is the first time the function is running for this order.
    if (!originalOrder.settled) {
      // this triggers a websocket event to notify the frontend
      orderToProcess = await this.app.service('order').patch(orderId, paymentDetails)
    }
    if (source === 'api') {
      // when calling via REST api, we don't wait for the detailed processing to finish
      // this is done to show the user quick response that the payment was successful, and the order is processing
      this.detailedProcessing(orderToProcess)
    } else {
      await this.detailedProcessing(orderToProcess)
    }
  }

  async detailedProcessing(order: any) {
    const currentRetryCount = order.retryInfo?.count || 0
    const orderId = order._id
    const session = await startTransactionSession(this.app)
    let refundInfo: any = null

    try {
      const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}

      refundInfo = await this.app.service('order').completeOrder(order, transactionParams)
      await commitTransactionSession(session, this.app, transactionParams)
    } catch (error: any) {
      console.log('Error during order processing:', error)
      await rollbackTransactionSession(session)

      // do the updates like gift card refund here, so that if fails can be retried again.

      // --- Error Analysis ---
      let decision = 'REFUND' // Default to refund for safety
      let retryReason = ''

      if (error?.hasErrorLabel && (error.hasErrorLabel('TransientTransactionError') || error.hasErrorLabel('UnknownTransactionCommitResult'))) {
        decision = 'RETRY'
        retryReason = error.hasErrorLabel('TransientTransactionError') ? 'TransientTransactionError' : 'UnknownTransactionCommitResult'
        retryReason += ' - ' + error?.message
      } else if (['ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED'].includes(error?.code)) {
        decision = 'RETRY'
        retryReason = 'NetworkIssue - ' + error?.message
      } else {
        decision = 'REFUND'
      }

      if (decision === 'RETRY' && currentRetryCount < this.maxRetryCount) {
        await this.app
          .service('order')
          .Model.updateOne({_id: orderId}, {$inc: {'retryInfo.count': 1}, $set: {'retryInfo.reason': retryReason, 'retryInfo.updatedAt': new Date()}})
        throw error // Re-throw the original error to trigger the webhook retry
      } else {
        // This block is reached for permanent errors OR if max retries have been exceeded.
        await this.handleFailedOrder(order)
      }
    } finally {
      this.releaseLock(orderId)
    }
  }

  async releaseLock(orderId: string) {
    try {
      await this.app.service('order').Model.updateOne({_id: orderId}, {$unset: {processingLockExpiresAt: ''}})
    } catch (error) {}
  }

  // Issue full refund for all products in the order
  async handleFailedOrder(order: any) {
    try {
      if (!Array.isArray(order.links)) return
      // Calculate total refund amount and collect product details
      let totalRefundAmount = 0
      const refundLinkName: string[] = []
      const refundLinkCover: string[] = []
      let myLinks = Acan.clone(order.links)
      for (const link of myLinks || []) {
        totalRefundAmount += link.price || 0
        if (link.name) {
          refundLinkName.push(link.name)
        }
        if (link.cover) {
          refundLinkCover.push(link.cover)
        }
        link.removed = true
        link.refundPrice = link.price
      }

      const {cashRefund, giftCardRefund} = calculateRefundBreakUp(
        {
          price: order.price,
          giftCard: order.priceBreakdown?.giftCard,
          cash: order.priceBreakdown?.cash,
          refund: order.refund,
        },
        totalRefundAmount
      )

      await this.processDbUpdate({order, myLinks, cashRefund, giftCardRefund, refundLinkName, refundLinkCover})

      if (cashRefund > 0) {
        const refundInfo = {
          amount: cashRefund,
          refundLinkName: refundLinkName,
          refundLinkCover: refundLinkCover,
          giftCardRefunded: giftCardRefund,
        }
        console.log('onto cash refund')
        await this.processRefund(order._id, refundInfo, order)
      }
    } catch (error) {
      const currentRetryCount = order.retryInfo?.count || 0
      console.error('handleFailedOrder: ', error)
      if (currentRetryCount < this.maxRetryCount) {
        console.error('handleFailedOrder: throw error for retry')
        throw error
      }
      console.error('handleFailedOrder: max_retries')
    }
  }
  /*
  - gift card refund amount in email
  */
  async processDbUpdate({order, updatedLinks, cashRefund, giftCardRefund, refundLinkName, refundLinkCover}: any) {
    const trxnParams: TxnParams = {}
    console.log('processDbUpdate', cashRefund, giftCardRefund, order.priceBreakdown?.giftCard)
    if (giftCardRefund > 0 || order.priceBreakdown?.giftCard > 0) {
      const session = await startTransactionSession(this.app)
      trxnParams.mongoose = {session}
      console.log('trxn_Failed_Update', !!session)
    }
    try {
      const promises = []
      const patchData: any = {
        status: 504,
        paid: 2,
        links: updatedLinks,
      }
      if (cashRefund > 0) {
        patchData.refundRequired = {
          amount: cashRefund,
          invalidLinks: order.links,
        }
      }
      if (giftCardRefund > 0) {
        promises.push(
          this.app.service('gift-card-log').createGiftCardLog(
            {
              uid: order.buyer,
              tab: 'earn',
              source: 'order_failed_refund',
              category: 'refund',
              value: giftCardRefund,
              businessId: order._id.toString(),
              isSchool: order.isSchool,
            },
            trxnParams
          )
        )
        patchData.$push = {refund: {method: 'giftCard', amount: giftCardRefund, executed: true, status: 503, createdAt: new Date(), executedAt: new Date()}}
      }
      if (order.priceBreakdown?.giftCard > 0) {
        promises.push(this.app.service('gift-card-log').releaseReservedBalance(order.priceBreakdown.giftCard, order.buyer, order.isSchool, trxnParams))
      }
      promises.push(this.app.service('order').patch(order._id, patchData, trxnParams))
      console.log('promises', promises.length)
      await Promise.all(promises)
      console.log('update done', !!trxnParams.mongoose?.session, new Date())
      await commitTransactionSession(trxnParams.mongoose?.session, this.app, trxnParams)
      console.log('commit done', new Date())
      if (giftCardRefund > 0 && cashRefund <= 0) {
        this.sendRefundSuccessNotification({order, amount: 0, refundLinkName, refundLinkCover, giftCardRefunded: giftCardRefund})
      }
    } catch (error: any) {
      await rollbackTransactionSession(trxnParams.mongoose?.session)
      if (error?.hasErrorLabel && (error.hasErrorLabel('TransientTransactionError') || error.hasErrorLabel('UnknownTransactionCommitResult'))) {
        console.log('TransientTransactionError', new Date())
      }
      console.error('processDbUpdate error', error?.message)
      throw error
    }
  }

  // Process refund for invalid links
  async processRefund(orderId: string, refundInfo: any, orderDoc?: any, isRetry?: boolean) {
    try {
      const order: any = orderDoc || (await this.app.service('order').Model.findOne({_id: orderId}))
      if (!order) {
        return
      }
      const {amount, refundLinkName = [], refundLinkCover = [], giftCardRefunded = 0} = refundInfo

      if (amount <= 0) return

      let refundSuccess = false
      // throw new Error('Error in refund')
      // Process refund via payment provider
      if (order.payMethod.indexOf('paypal') > -1) {
        const refundResult: any = await this.app.service('paypal').get('refund', {
          query: {id: order.paypalId, amount: (amount / 100).toFixed(2)},
        })
        console.log('refundResult', refundResult)
        if (refundResult.success) {
          refundSuccess = true
        }
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundSuccess = true
      }
      // throw new Error('Error after refund')
      if (refundSuccess) {
        await this.handleRefundSuccess(order, amount, refundLinkName, refundLinkCover, giftCardRefunded)
      } else {
        if (isRetry) throw new Error('Refund failed')
        await this.handleRefundFailure(orderId, amount, refundLinkName)
      }
    } catch (error) {
      if (isRetry) throw new Error('Refund failed')
      await this.handleRefundFailure(orderId, refundInfo.amount, refundInfo.refundLinkName)
    }
  }

  // Handle successful refund
  async handleRefundSuccess(order: any, amount: number, refundLinkName: string[], refundLinkCover: string[], giftCardRefunded: number) {
    try {
      let refundList: any = []
      let notifyCustomer = false

      if (order.payMethod.indexOf('paypal') > -1) {
        refundList.push({
          method: 'paypal',
          amount: amount,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: 503,
        })
        notifyCustomer = true
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundList.push({
          method: 'braintree',
          amount: amount,
          createdAt: new Date(),
          executed: false,
          status: 503,
        })
      }
      await this.app.service('order').patch(order._id, {
        $push: {refund: {$each: refundList}},
        $unset: {refundRequired: ''},
      })

      if (notifyCustomer && refundLinkName?.length > 0) {
        // Send success notification to customer
        await this.sendRefundSuccessNotification({order, amount, refundLinkName, refundLinkCover, giftCardRefunded})
      }
    } catch (error) {}
  }

  // Handle failed refund
  async handleRefundFailure(orderId: string, amount: number, refundLinkName: string[]) {
    // Send admin notification for manual intervention
    await this.sendRefundFailureAdminNotification(orderId, amount)

    // Send customer notification about delay
    await this.sendRefundDelayCustomerNotification(orderId, refundLinkName)
  }

  // Send refund success notification to customer
  async sendRefundSuccessNotification({order, amount, refundLinkName, refundLinkCover, giftCardRefunded}: any) {
    try {
      const userId = order.isSchool ? order.schoolAdmin : order.buyer
      const user = await this.app.service('users').uidToInfo(userId)
      const url = `${SiteUrl}/v2/order/payHistory/${order._id}`
      const url2 = `${SiteUrl}/v2/order/detail/${order._id}`
      await this.app.service('notice-tpl').send(
        'OrderRefundSuccess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          gift_card_amount: (giftCardRefunded / 100).toFixed(2),
          cash_amount: (amount / 100).toFixed(2),
          no: order.no,
          amount: (order.price / 100).toFixed(2),
          date: order.paidAt ? new Date(order.paidAt) : new Date(),
          url: url,
          link_name: refundLinkName.join(', '),
          url2: url2,
          image: hashToUrl(refundLinkCover[0] || ''),
          addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
        }
      )
    } catch (error) {}
  }

  // Send refund delay notification to customer
  async sendRefundDelayCustomerNotification(orderId: string, refundLinkName: string[]) {
    try {
      const order: any = await this.app.service('order').Model.findOne({_id: orderId})
      const userId = order.isSchool ? order.schoolAdmin : order.buyer
      const user = await this.app.service('users').uidToInfo(userId)
      const url = `${SiteUrl}/v2/order/detail/${orderId}`

      await this.app.service('notice-tpl').send(
        'OrderRefundInProcess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          link_name: refundLinkName.join('<br>'),
          url: url,
        }
      )
    } catch (error) {}
  }

  // Send admin notification for refund failure
  async sendRefundFailureAdminNotification(orderId: string, amount: number) {
    try {
      await Promise.all(
        this.adminEmails.map(async (email) => {
          await this.app.service('notice-tpl').send(
            'OrderRefundFailed',
            {_id: '', email: email},
            {
              orderId: orderId,
              amount: amount,
              url: `${SiteUrl}/v2/order/detail/${orderId}`,
              sys_url: `${SiteUrl}/v2/sys/order-failure-logs`,
            }
          )
        })
      )
    } catch (error) {}
  }

  // Retry failed refunds - cron job method
  async retryFailedRefunds() {
    try {
      // Find orders with pending refunds (older than 10 minutes to avoid immediate retries)
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
      const ordersWithPendingRefunds = await this.app
        .service('order')
        .Model.find({
          'refundRequired.createdAt': {$lt: tenMinutesAgo},
          'refundRequired.escalated': false,
        })
        .sort({'refundRequired.createdAt': 1})
        .limit(50)
      for (const orderDoc of ordersWithPendingRefunds) {
        try {
          const order: any = orderDoc
          const refundInfo = {
            amount: order.refundRequired.amount,
            invalidLinks: order.refundRequired.invalidLinks,
            refundLinkName: order.refundRequired.refundLinkName || order.refundRequired.invalidLinks?.map((link: any) => link.name) || [],
            refundLinkCover: order.refundRequired.refundLinkCover || order.refundRequired.invalidLinks?.map((link: any) => link.cover) || [],
            giftCardRefunded: order.refundRequired.giftCardRefunded || 0,
          }
          // For PayPal orders, check if refund already exists before retrying
          if (order.paypalId) {
            try {
              const refundCheck = await this.app.service('paypal').checkRefundExists(order.paypalId)
              if (refundCheck.exists) {
                await this.handleRefundSuccess(order, refundInfo.amount, refundInfo.refundLinkName, refundInfo.refundLinkCover, refundInfo.giftCardRefunded)
                continue
              }
            } catch (error) {
              // Continue with retry if we can't check status
            }
          }
          await this.processRefund(order._id.toString(), refundInfo, orderDoc, true)
        } catch (error) {
          const order: any = orderDoc

          // If refund has been pending for more than 24 hours, escalate to admin
          const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
          if (order.refundRequired.createdAt < twentyFourHoursAgo) {
            await this.sendRefundFailureAdminNotification(order._id.toString(), order.refundRequired.amount)

            // Mark as escalated to prevent repeated admin notifications
            await this.app.service('order').Model.updateOne({_id: order._id}, {$set: {'refundRequired.escalated': true}})
          }
        }
      }
    } catch (error) {}
  }
}
