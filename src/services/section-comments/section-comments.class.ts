// src/services/journal-comments/journal-comments.class.ts

import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Id, Params} from '@feathersjs/feathers'
import {BadRequest, Forbidden, NotFound} from '@feathersjs/errors'

export class SectionComments extends Service {
  app: Application
  options: any
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.options = options
    this.app = app
  }
  async create(data: any, params: Params) {
    const user = params.user ?? {}
    const {sectionId, cover, mime} = data

    if (!sectionId || !cover) {
      throw new BadRequest('sectionId and cover are required')
    }

    const fileEntry = {
      uid: user._id,
      name: user.name || [],
      nickname: user.nickname || '',
      avatar: user.avatar || '',
      cover,
      mime,
      uploadedTime: new Date(),
    }

    const fileData = {
      uid: user._id,
      sectionId,
      files: fileEntry,
    }
    return super.create(fileData, params)
  }

  async addComment(id: string, data: {comment: string}, params: Params) {
    const uid = params.user?._id
    if (!uid) throw new Error('User not authenticated')

    const newComment = {
      uid,
      comment: data.comment,
      createdAt: new Date(),
    }

    return await this.Model.findByIdAndUpdate(
      id,
      {
        $push: {
          comments: {
            $each: [newComment],
            $position: 0,
          },
        },
      },
      {new: true}
    )
  }

  async remove(id: Id, params: Params) {
    const user = params.user ?? {}

    if (!id || typeof id !== 'string') {
      throw new BadRequest('A valid comment ID must be provided')
    }

    const existing = (await this.Model.findOne({_id: id, del: {$ne: true}}).lean()) as any

    if (!existing || existing.uid !== user._id) {
      throw new Forbidden('You can only delete your own comment')
    }

    await this.Model.findByIdAndDelete(id)

    return {success: true}
  }
}
