import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks
import {HookContext} from '@feathersjs/feathers'
import logger from '../../logger'

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [
      async (context: HookContext) => {
        const {comment} = context.data
        if (comment) {
          const result = await context.service.addComment(context.id, context.data, context.params)

          context.result = result

          return context
        }

        return context
      },
    ],
    remove: [],
  },

  after: {
    all: [],
    find: [
      async (context: HookContext) => {
        const {connection} = context.params
        const result = context.result
        for (const o of result.data) {
          if (connection && o && o._id) {
            const sectionCommentsId = o._id.toString()
            context.app.channel(`section-comments/${sectionCommentsId}`).join(connection)
          }
        }

        return context
      },
    ],
    get: [],
    create: [],
    update: [],
    patch: [
      async (context: HookContext) => {
        const {connection} = context.params
        const result = context.result
        const sectionCommentsId = result._id.toString()
        if (connection) {
          context.app.channel(`section-comments/${sectionCommentsId}`).join(connection)
        }
      },
    ],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
