import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {SectionComments} from './section-comments.class'
import createModel from '../../models/section-comments.model'
import hooks from './section-comments.hooks'

declare module '../../declarations' {
  interface ServiceTypes {
    'section-comments': SectionComments & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    multi: false,
    paginate: app.get('paginate'),
  }

  app.use('/section-comments', new SectionComments(options, app))

  const service = app.service('section-comments')
  service.hooks(hooks)

  // Custom socket publisher for section-comments
  service.publish('patched', (data) => {
    const sectionCommentsId = data._id
    return [app.channel(`section-comments/${sectionCommentsId}`)]
  })
}
