import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {NotFound, BadRequest} from '@feathersjs/errors'
import {TxnParams} from '../../dbTransactions'

export class Section extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async getDetails(_id: string, params?: Params) {
    const section = await this.Model.findById(_id)
    if (!section) throw new NotFound('Section not found')
    return section
  }

  async create(data: any, params?: Params) {
    return super.create(data, params)
  }

  async patch(_id: string, data: any, params?: Params) {
    if (data.serviceProviderId) {
      return this.addUserInfoToProvider(_id, data, params!)
    } else if (data.lastOne) {
      return this.updateLastSection(_id, data, params!)
    }

    const result = await super.patch(_id, data, params)

    // Send notification only when markAsCompleted is explicitly set to true
    if (data.markAsCompleted === true && result?.serviceTaskId) {
      await this.sendSectionCompletionNotification(result.serviceTaskId, params!)
    }

    return result
  }

  async addUserInfoToProvider(id: string, data: {serviceProviderId: string}, params: Params) {
    const userId = params.user?._id || params.user?.id
    if (!userId) throw new Error('User not authenticated')
    const user = await this.app.service('users').get(data.serviceProviderId)

    const newProvider = {
      userId: data.serviceProviderId,
      name: user.name || [],
      nickname: user.nickname || '',
      avatar: user.avatar || '',
      assignedTime: new Date(),
    }

    return await this.Model.findByIdAndUpdate(id, {$push: {serviceProviderDetails: newProvider}}, {new: true})
  }

  async extUser(result: any, params: Params) {
    if (result.uid) result.bookerInfo = await this.app.service('users').uidToInfo(result.uid)
    if (result.servicer) result.servicerInfo = await this.app.service('users').uidToInfo(result.servicer)
    return result
  }

  async cron1({}: any, params?: Params): Promise<any> {
    this.markSectionsComplete(params!)
  }

  async markSectionsComplete(params?: Params) {
    await this.Model.find({markAsCompleted: true, status: 'ongoing', markAsCompletedTime: {$lt: new Date(Date.now() - 12 * 60 * 60 * 1000)}}).then(
      async (rs: any) => {
        for (let i = 0; i < rs.length; i++) {
          const section = rs[i]
          await this.Model.updateOne(
            {_id: section._id},
            {
              status: 'completed',
              availableCredits: 0,
              completedTime: new Date(),
            }
          )
          const nextSection = await this.Model.findOne({serviceTaskId: section.serviceTaskId, sectionNo: section.sectionNo + 1})
          if (nextSection) {
            await this.Model.updateOne({_id: nextSection._id}, {status: 'ongoing'})
          } else {
            await this.updateLastSection(section.serviceTaskId, {lastOne: true, curServicer: section.servicer, bookingId: section.bookingId}, params!)
          }
        }
      }
    )
  }

  async updateLastSection(
    id: string,
    data: {lastOne: boolean; curServicer: string; bookingId: string; markAsCompleted?: boolean; status?: string},
    params: Params
  ) {
    try {
      const sectionsToTrack = await this.Model.find({
        serviceTaskId: id,
        completedBy: null,
      })

      const trackingDocs = sectionsToTrack.map((section: any) => {
        return {
          sectionId: section._id.toString(),
          sectionSnapshot: Acan.clone(section),
          status: 'completed',
          completedTime: section.completedTime || new Date(),
          cancelledTime: null,
          cancelledByServicer: false,
          creditedPoints: section.creditedPoints || 0,
          debitedPoints: section.debitedPoints || 0,
          bookingId: section.bookingId,
          packUser: section.serviceTaskId,
          booker: section.uid,
          servicer: section.servicer,
        }
      })

      await Promise.all([
        this.app.service('section-tracking').Model.insertMany(trackingDocs),
        this.Model.updateMany({serviceTaskId: id, completedBy: null}, {$set: {completedBy: data.curServicer}}),
        this.app.service('service-pack-user').Model.updateOne({_id: id}, {associatedTaskStatus: 'completed'}),
        this.app.service('service-booking').Model.updateOne({_id: data.bookingId}, {completedTime: new Date()}),
      ])

      if (data.markAsCompleted === true) {
        await this.sendSectionCompletionNotification(id, params)
      }
    } catch (error) {
      throw new BadRequest('Failed to update the last section')
    }
  }

  async sendSectionCompletionNotification(serviceTaskId: string, params: Params) {
    try {
      // Get the service-pack-user data to find the booker and service details
      const servicePackUser: any = await this.app.service('service-pack-user').Model.findById(serviceTaskId)
      if (!servicePackUser) return

      const bookerId = servicePackUser.uid
      const serviceType = servicePackUser.snapshot?.type || 'service'
      const associatedTaskName = servicePackUser.snapshot?.name || 'Associated Task'

      const booker: any = await this.app.service('users').Model.findOne({_id: bookerId})
      if (!booker) return

      let url = ''
      //       Buyer , Student : Personal account / My purchased / My Associated task / ongoing tab / details page / navigate to the ongoing section

      // Buyer, Teacher : Personal account / I participate / My Associated task / ongoing tab / details page / navigate to the ongoing section
      if (booker.roles?.includes('student')) {
        url = `${SiteUrl}/v2/study/purchased/view/${serviceTaskId}?tab=myAssociatedTask&subtab=ongoing`
      } else if (booker.roles?.includes('teacher')) {
        url = `${SiteUrl}/v2/home/<USER>/view/${serviceTaskId}?tab=taskManagement&subtab=ongoing`
      }

      // Send notification email
      this.app.service('notice-tpl').mailto(
        'AssociatedTaskSectionCompleted(ByProvider)',
        booker,
        {
          username: booker?.name.join(' '),
          service_type: serviceType,
          associated_task_name: associatedTaskName,
          url,
        },
        params.user?._id
      )
    } catch (error) {
      console.error('Failed to send section completion notification:', error)
    }
  }

  async getDeficitCredit({sectionId, sectionDoc}: {sectionId: string; sectionDoc?: any}, params: Params) {
    const section: any = sectionDoc || (await this.Model.findOne({_id: sectionId, uid: params.user?._id, status: 'pending'}).lean())
    if (!section) throw new NotFound('Section not found')

    const {availableCredits, salesPrice} = section
    const deficit = salesPrice - (availableCredits || 0)

    return {deficitCredit: deficit > 0 ? deficit * 100 : 0, section}
  }

  async buyDeficitCredit({sectionId, amount, order}: any, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const section: any = await this.Model.findOneAndUpdate(
      {_id: sectionId},
      {$inc: {availableCredits: amount}, $push: {creditSources: {orderId: order, creditAmount: amount}}},
      options
    ).lean()
    if (!section) {
      return Promise.reject(new Error('Section not found'))
    }
  }

  async getRemainingSections({packUserId}: {packUserId: string}, params: TxnParams) {
    const options = Acan.getTxnOptions(params)
    const sections: any = await this.Model.find({serviceTaskId: packUserId, 'creditSources.0': {$exists: false}, uid: params.user?._id}, null, options).lean()
    if (!sections || sections.length === 0) {
      throw new NotFound('No remaining sections found')
    }
    const price = sections.reduce((total: number, section: any) => total + section.salesPrice, 0)

    return {sections, price}
  }

  async buyRemainingSections({packUserId, order}: {packUserId: string; order: string}, params: Params) {
    const {sections} = await this.getRemainingSections({packUserId}, params)
    if (!sections || sections.length === 0) {
      throw new NotFound('No remaining sections found')
    }

    await Promise.all(sections.map((section: any) => this.buyDeficitCredit({sectionId: section._id, amount: section.salesPrice, order: order}, params)))
  }
}
